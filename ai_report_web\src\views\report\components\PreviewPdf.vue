<template>
  <div class="generate-pdf-container">
    <!-- Loading 遮罩层 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载报告数据...</div>
        <div class="loading-progress">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: loadingProgress + '%' }"
            ></div>
          </div>
          <div class="progress-text">{{ loadingProgress }}%</div>
        </div>
      </div>
    </div>

    <div class="report-container" v-show="!isLoading">
      <!-- 报告封面头部区域 -->
      <div class="report-header">
        <div class="logo">
          <img
            src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf_logo.png"
          />
        </div>
        <div class="stu-name">姓名：{{ reportForm.name }}</div>
      </div>
      <div class="report-content-container">
        <!-- 报告内容页面 -->
        <div class="report-content">
          <div class="first-step">
            <div class="tite-img-container">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/first-step-title.png"
                alt="第一步"
                class="step-title-bg"
              />
            </div>

            <!-- 第一步：个人基础信息 -->
            <div class="step-section">
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">1</span>
                  <span class="step-text">个人基础信息</span>
                </div>
              </div>
              <div class="step-content">
                <div class="form-grid">
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">学员姓名：</span>
                      <span class="value">{{ reportForm.name }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">性别：</span>
                      <span class="value">{{ reportForm.sex }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">本科院校：</span>
                      <span class="value">{{
                        reportForm.undergraduateSchool
                      }}</span>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">本科专业：</span>
                      <span class="value">{{
                        reportForm.undergraduateMajor
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">目标专业：</span>
                      <span class="value">{{
                        truncateText(reportForm.targetMajor, 6)
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">专业代码：</span>
                      <span class="value">{{
                        truncateText(reportForm.majorCode, 6)
                      }}</span>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">联系方式：</span>
                      <span class="value">{{ reportForm.phone }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">考研年份：</span>
                      <span class="value">{{ reportForm.examYear }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">跨专业：</span>
                      <span class="value">{{
                        reportForm.isMultiDisciplinary
                      }}</span>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item full-width">
                      <span class="label">培养方式：</span>
                      <span class="value">{{
                        reportForm.educationalStyle
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第二步：本科成绩情况 -->
            <div class="step-section">
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">2</span>
                  <span class="step-text">本科成绩情况</span>
                </div>
              </div>
              <div class="step-content">
                <div class="form-undergraduate">
                  <div
                    class="form-item"
                    v-for="item in reportForm.undergraduateTranscript"
                    :key="item.id"
                  >
                    <span class="label">{{ item?.title }}：</span>
                    <span class="value">{{ item?.score || "" }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第三步：英语基础 -->
            <div class="step-section">
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">3</span>
                  <span class="step-text">英语基础</span>
                </div>
              </div>
              <div class="step-content">
                <div class="form-grid">
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">高考英语成绩：</span>
                      <span class="value">{{
                        reportForm.englishScore || ""
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">大学四级：</span>
                      <span class="value">{{ reportForm.cet4 || "" }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">大学六级：</span>
                      <span class="value">{{ reportForm.cet6 || "" }}</span>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">托福：</span>
                      <span class="value">{{
                        reportForm.tofelScore || ""
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">雅思：</span>
                      <span class="value">{{
                        reportForm.ieltsScore || ""
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">英语能力：</span>
                      <span class="value">{{
                        reportForm.englishLevel || ""
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第四步：目标院校倾向 -->
            <div class="step-section">
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">4</span>
                  <span class="step-text">目标院校倾向</span>
                </div>
              </div>
              <div class="step-content">
                <div class="form-grid">
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">地区倾向：</span>
                      <span class="value">{{ reportForm.region }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">省份倾向：</span>
                      <span class="value">{{
                        reportForm.intendedSchools
                      }}</span>
                    </div>
                  </div>
                  <div class="form-row">
                    <div class="form-item">
                      <span class="label">梦校：</span>
                      <span class="value">{{
                        reportForm.targetSchoolName
                      }}</span>
                    </div>
                    <div class="form-item">
                      <span class="label">院校层次：</span>
                      <span class="value">{{ reportForm.schoolLevel }}</span>
                    </div>
                  </div>
                  <div class="form-row full-width">
                    <div class="form-item">
                      <span class="label">专业课指定参考书：</span>
                      <span class="value">{{ reportForm.referenceBooks }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第五步：考研成绩预估 -->
            <div class="step-section">
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">5</span>
                  <span class="step-text">考研成绩预估</span>
                </div>
              </div>
              <div class="step-content">
                <div class="score-table">
                  <table>
                    <thead>
                      <tr>
                        <th>政治</th>
                        <th>英语</th>
                        <th>业务课一</th>
                        <th>业务课二</th>
                        <th>总分</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>{{ reportForm.politics || "" }}</td>
                        <td>{{ reportForm.englishS || "" }}</td>
                        <td>{{ reportForm.englishType || "" }}</td>
                        <td>{{ reportForm.mathType || "" }}</td>
                        <td>{{ reportForm.totalScore || "" }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="weak-modules">
                  <span class="label">个性化需求：</span>
                  <span class="value">{{
                    reportForm.personalNeeds || ""
                  }}</span>
                </div>

                <div class="weak-modules">
                  <span class="label">薄弱环节：</span>
                  <span class="value">{{ reportForm.weakModules || "" }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二页专业分析 -->
        <!-- 报告内容页面 -->
        <div class="report-content">
          <div class="first-step">
            <div class="tite-img-container">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/step_two_major.png"
                alt="第二步"
                class="step-title-bg"
              />
            </div>

            <!-- A区图表 -->
            <div class="chart-section">
              <div class="chart-header">
                <span class="region-label">A区</span>
                <div class="chart-info">
                  <span
                    >一级学科：{{
                      nationalLineData?.first_level_discipline
                    }}</span
                  >
                  <span>学门门类：{{ nationalLineData?.subject_name }}</span>
                </div>
              </div>
              <div class="chart-container">
                <div ref="chartA" class="echarts-box"></div>
              </div>
            </div>

            <!-- B区图表 -->
            <div class="chart-section">
              <div class="chart-header">
                <span class="region-label">B区</span>
                <div class="chart-info">
                  <span
                    >一级学科{{
                      nationalLineData?.first_level_discipline
                    }}</span
                  >
                  <span>学门门类：{{ nationalLineData?.subject_name }}</span>
                </div>
              </div>
              <div class="chart-container">
                <div ref="chartB" class="echarts-box"></div>
              </div>
            </div>

            <!-- 第三部分 -->
            <div class="tite-img-container">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/schools_prev.png"
                alt="第三步"
                class="step-title-bg"
              />
            </div>

            <!-- 院校基础信息表格 -->
            <div class="school-info-section">
              <div class="school-table-container">
                <table class="school-info-table">
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>院校名称</th>
                      <th>所在地区</th>
                      <th>所在城市</th>
                      <th>学院</th>
                      <th>专业</th>
                      <th>专业代码</th>
                      <th>最低分</th>
                      <th>最低总分差</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(school, index) in schoolListData"
                      :key="school.id"
                    >
                      <td>{{ index + 1 }}</td>
                      <td>
                        <div class="school-name">
                          <span>{{ school.school_name }}</span>
                          <div class="school-tags">
                            <span
                              class="tag tag-double"
                              v-if="school.tag_double"
                              >双一流</span
                            >
                            <span class="tag tag-985" v-if="school.tag_985"
                              >985</span
                            >
                            <span class="tag tag-211" v-if="school.tag_211"
                              >211</span
                            >
                          </div>
                        </div>
                      </td>
                      <td>{{ school.region }}</td>
                      <td>{{ school.city }}</td>
                      <td>{{ school.college }}</td>
                      <td>{{ school.major_name }}</td>
                      <td>{{ school.major_code }}</td>
                      <td>{{ school.min_score }}</td>
                      <td>{{ school.score_diff }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- 第四页及后续：院校详细分析 -->
        <div
          class="report-content"
          v-for="(school, index) in realRecommendData"
          :key="index"
        >
          <div class="first-step">
            <!-- 第三部分 -->
            <div class="tite-img-container">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/school_analysis.png"
                alt="第四步院校分析"
                class="step-title-bg"
              />
            </div>

            <!-- 院校详情卡片 -->
            <div class="school-detail-card">
              <div class="school-header">
                <!-- <div class="school-logo" v-show="school.school_info?.logo">
                <img :src="'https://' + school.school_info?.logo || '@/assets/images/school.png'" alt="学校logo" />
              </div> -->
                <div class="school-logo">
                  <img src="@/assets/images/school.png" alt="学校logo" />
                </div>
                <div class="school-info">
                  <div class="school-title">
                    <h2>{{ school.school_name || "" }}</h2>
                    <span class="school-location">{{
                      school.school_info?.address || ""
                    }}</span>
                  </div>
                  <div class="school-tags-row">
                    <span
                      class="tag tag-double"
                      v-if="school.school_info?.is_dual_class"
                      >双一流</span
                    >
                    <span class="tag tag-985" v-if="school.school_info?.tag_985"
                      >985</span
                    >
                    <span class="tag tag-211" v-if="school.school_info?.tag_211"
                      >211</span
                    >
                    <span class="major-name">{{ school.major_name }}</span>
                    <span class="score-diff"
                      >分差:{{ getDiffScore(parseInt(school.school_id)) }}</span
                    >
                  </div>
                </div>
              </div>

              <template v-if="Object.keys(school.basic_info!).length != 0">
                <h3 class="section-title">院校情况</h3>
                <div class="school-detail-section">
                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>总成绩计算公式</h4>
                      <p>{{ school.basic_info?.score_formula }}</p>
                    </div>
                  </div>

                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>学制</h4>
                      <p>{{ school.basic_info?.study_years }}</p>
                    </div>
                  </div>

                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>学费</h4>
                      <p>学费：{{ school.basic_info?.tuition_fee }}</p>
                    </div>
                  </div>
                </div>

                <h3 class="section-title">初试模块</h3>
                <div class="school-detail-section">
                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>初试考试科目</h4>
                      {{ school.basic_info?.exam_range }}
                    </div>
                  </div>

                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>考试专业课参考书</h4>
                      <p>{{ school.basic_info?.reference_books }}</p>
                    </div>
                  </div>

                  <div class="detail-item">
                    <div class="item-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="item-content">
                      <h4>复试考核内容</h4>
                      <p>{{ school.basic_info?.retest_score_requirement }}</p>
                    </div>
                  </div>
                </div>
              </template>

              <div class="step-content-zs-detail">
                <template
                  v-if="
                    school.admission_data && school.admission_data.length > 0
                  "
                >
                  <div class="admission-title">招生情况</div>
                  <div class="enroll-students-table">
                    <table class="admission-table">
                      <thead>
                        <tr>
                          <th>年份</th>
                          <th>招生计划</th>
                          <th>一志愿复试</th>
                          <th>拟录取</th>
                          <th>录取比</th>
                          <th>调剂人数</th>
                          <th>最高分</th>
                          <th>最低分</th>
                          <th>平均分</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="(item, idx) in school.admission_data"
                          :key="idx"
                        >
                          <td>{{ item.year }}</td>
                          <td>{{ item.planCount }}</td>
                          <td>{{ item.examCount }}</td>
                          <td>{{ item.admitCount }}</td>
                          <td>{{ item.ratio }}</td>
                          <td>{{ item.studentCount }}</td>
                          <td>{{ item.highestScore }}</td>
                          <td>{{ item.lowestScore }}</td>
                          <td>{{ item.averageScore }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </template>
              </div>

              <!-- 招生情况 -->
              <div class="step-content-zs">
                <template
                  v-if="school.current_year_retest_list.list.length > 0"
                >
                  <!-- 一志愿考试名单标题 -->
                  <div class="admission-title">一志愿复试名单</div>

                  <!-- 一志愿考试名单表格 -->
                  <table class="admission-table">
                    <thead>
                      <tr>
                        <th>编号</th>
                        <th>学生姓名</th>
                        <th>政治</th>
                        <th>英语</th>
                        <th>专业课一</th>
                        <th>专业课二</th>
                        <th>初试成绩</th>
                        <th>是否一志愿</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="(item, idx) in school.current_year_retest_list
                          .list"
                        :key="idx"
                      >
                        <td>{{ idx + 1 }}</td>
                        <td>{{ item.name + "**" }}</td>
                        <td>{{ item.politics_score }}</td>
                        <td>{{ item.english_score }}</td>
                        <td>{{ item.major1_score }}</td>
                        <td>{{ item.major2_score }}</td>
                        <td>{{ item.initial_score }}</td>
                        <td>{{ item.admission_status }}</td>
                      </tr>
                    </tbody>
                  </table>
                </template>

                <template v-if="school.basic_info?.retest_content">
                  <!-- 复试模块标题 -->
                  <div class="admission-title">复试模块</div>

                  <!-- 复试模块内容 -->
                  <div class="reexam-container">
                    <div class="reexam-card">
                      <div class="reexam-header">
                        <img
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="标签图标"
                        />
                        <div class="reexam-title">复试考试内容</div>
                      </div>
                      <div class="reexam-content">
                        {{ school.basic_info?.retest_content }}
                      </div>
                    </div>

                    <!-- <div class="reexam-card">
                      <div class="reexam-header">
                        <img
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="标签图标"
                        />
                        <div class="reexam-title">录取要求</div>
                      </div>
                      <div class="reexam-content">
                        <p v-if="school.basic_info?.admission_requirements">
                          {{ school.basic_info?.admission_requirements }}
                        </p>
                        <p v-else>暂无数据</p>
                      </div>
                    </div> -->
                  </div>
                </template>

                <template
                  v-if="school.current_year_admission_list?.list.length > 0"
                >
                  <!-- 拟录取名单标题 -->
                  <div class="admission-title">拟录取名单</div>

                  <!-- 拟录取名单表格 -->
                  <table class="admission-table">
                    <thead>
                      <tr>
                        <th>编号</th>
                        <th>学生姓名</th>
                        <th>初试成绩</th>
                        <th>复试成绩</th>
                        <th>两项总成绩</th>
                        <th>一志愿学校</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="(item, idx) in school.current_year_admission_list
                          .list"
                        :key="idx"
                      >
                        <td>{{ idx + 1 }}</td>
                        <td>{{ item.name + "**" }}</td>
                        <td>{{ item.initial_score }}</td>
                        <td>{{ item.retest_score }}</td>
                        <td>{{ item.total_score }}</td>
                        <td>{{ item.first_choice_school }}</td>
                      </tr>
                    </tbody>
                  </table>
                </template>

                <!-- 综合建议标题 -->
                <div class="admission-title">综合建议</div>

                <!-- 综合建议内容 -->
                <div class="reexam-container">
                  <div class="reexam-card">
                    <div class="reexam-header">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                      <div class="reexam-title">竞争难度分析</div>
                    </div>
                    <div class="reexam-content">
                      {{ school.difficulty_analysis }}
                    </div>
                  </div>

                  <div class="reexam-card">
                    <div class="reexam-header">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                      <div class="reexam-title">备考目标建议</div>
                    </div>
                    <div class="reexam-content">
                      {{ school.suggest }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 页脚 -->
        </div>

        <!-- 推荐综合性价比高的院校页面 -->
        <div class="report-content">
          <!-- 顶部绿色背景区域 -->

          <div class="first-step">
            <div class="recommend-school-wraper">
              <!-- 推荐综合性价比高的院校标题 -->
              <div class="step-num-tag">
                <span class="step-number">{{
                  realRecommendData.length + 1
                }}</span>
                <div class="tag-text">推荐综合性价比高的院校</div>
              </div>

              <!-- 推荐综合性价比高的院校内容 -->
              <div class="recommend-school-container">
                <div class="recommend-school-card">
                  <div class="recommend-school-header">
                    <div class="recommend-icon">
                      <img
                        src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                        alt="标签图标"
                      />
                    </div>
                    <div class="recommend-title">推荐原因</div>
                  </div>
                  <div class="recommend-school-content">
                    {{ highRecommendData[0]?.reason || "暂无推荐信息" }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 学习计划页面 -->
        <div
          class="report-content"
          v-if="studyPlanData"
          style="padding-bottom: 30px"
        >
          <!-- 顶部绿色背景区域 -->

          <div class="first-step">
            <!-- 学习计划标题图片 -->
            <div class="tite-img-container">
              <img
                src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/study-plan-title-bg.png"
                alt="学习计划"
                class="step-title-bg"
              />
            </div>

            <!-- 薄弱模块分析 -->
            <div
              class="step-section"
              v-if="
                studyPlanData.weakModuleAnalysis &&
                studyPlanData.weakModuleAnalysis.length > 0
              "
            >
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">01</span>
                  <span class="step-text">薄弱模块分析</span>
                </div>
              </div>
              <div class="step-content">
                <div class="weak-module-container">
                  <div
                    v-for="(
                      weakModule, moduleIndex
                    ) in studyPlanData.weakModuleAnalysis"
                    :key="moduleIndex"
                    class="weak-module-item"
                  >
                    <!-- 科目 -->
                    <div class="detail-section">
                      <div class="subtitle">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="标签图标"
                        />
                        <span class="subtitle-text">科目</span>
                      </div>
                      <p class="detail-text">{{ weakModule.subject }}</p>
                    </div>

                    <!-- 问题分析 -->
                    <div
                      class="analysis-section"
                      v-if="weakModule.problemAnalysis"
                    >
                      <div class="subtitle">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="标签图标"
                        />
                        <span class="subtitle-text">问题分析</span>
                      </div>
                      <p class="analysis-text">
                        {{ weakModule.problemAnalysis }}
                      </p>
                    </div>

                    <!-- 解决方案 -->
                    <div class="solutions-section" v-if="weakModule.solutions">
                      <div class="subtitle">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="标签图标"
                        />
                        <span class="subtitle-text">解决方案</span>
                      </div>
                      <div class="solutions-content">
                        <p class="solution-text">{{ weakModule.solutions }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 目标分数表格 -->
            <div class="target-score-section" v-if="targetScoreData">
              <div data-v-e1d12685="" class="step-header">
                <div data-v-e1d12685="" class="step-num-tag">
                  <img
                    data-v-e1d12685=""
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  /><span data-v-e1d12685="" class="step-number">02</span
                  ><span data-v-e1d12685="" class="step-text">各目标分数</span>
                </div>
              </div>

              <div class="target-score-table">
                <div class="score-row header-row">
                  <div class="score-cell">政治</div>
                  <div class="score-cell">英语</div>
                  <div class="score-cell">业务课一</div>
                  <div class="score-cell">业务课二</div>
                  <div class="score-cell">总分</div>
                </div>
                <div class="score-row data-row">
                  <div class="score-cell bottom-cell">
                    {{ targetScoreData.politics || 0 }}
                  </div>
                  <div class="score-cell bottom-cell">
                    {{ targetScoreData.english || 0 }}
                  </div>
                  <div class="score-cell bottom-cell">
                    {{ targetScoreData.business1 || 0 }}
                  </div>
                  <div class="score-cell bottom-cell">
                    {{ targetScoreData.business2 || 0 }}
                  </div>
                  <div class="score-cell bottom-cell">
                    {{ targetScoreData.total || 0 }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 学习规划模块 -->
            <div
              class="study-planning-section"
              v-if="
                studyPlanData.studyPlanning &&
                studyPlanData.studyPlanning.stages.length > 0
              "
            >
              <!-- 主标题 -->
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">03</span>
                  <span class="step-text">{{
                    studyPlanData.studyPlanning.title
                  }}</span>
                </div>
              </div>

              <!-- 阶段循环输出 -->
              <div
                v-for="(stage, stageIndex) in studyPlanData.studyPlanning
                  .stages"
                :key="stage.id"
                class="stage-section"
              >
                <!-- 阶段标题 -->
                <div class="stage-title">{{ stage.title }}</div>

                <div class="stage-content">
                  <!-- 模块容器循环输出 -->
                  <div
                    v-for="(module, moduleIndex) in stage.modules"
                    :key="module.id"
                    class="module-container"
                  >
                    <!-- 模块标题 -->
                    <div class="module-title">
                      <span class="module-title-text">{{ module.name }}</span>
                    </div>

                    <!-- 学习内容 -->
                    <div class="study-item" v-if="module.studyContent">
                      <div class="study-item-title">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="菱形图标"
                        />
                        <span class="study-item-title-text">学习内容</span>
                      </div>
                      <p class="study-item-content">
                        {{ module.studyContent }}
                      </p>
                    </div>

                    <!-- 学习方法 -->
                    <div class="study-item" v-if="module.studyMethod">
                      <div class="study-item-title">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="菱形图标"
                        />
                        <span class="study-item-title-text">学习方法</span>
                      </div>
                      <p class="study-item-content">{{ module.studyMethod }}</p>
                    </div>

                    <!-- 资料推荐 -->
                    <div class="study-item" v-if="module.studyMaterials">
                      <div class="study-item-title">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="菱形图标"
                        />
                        <span class="study-item-title-text">资料推荐</span>
                      </div>
                      <p class="study-item-content">
                        {{ module.studyMaterials }}
                      </p>
                    </div>

                    <!-- 要点提醒 -->
                    <div class="study-item" v-if="module.studyReminder">
                      <div class="study-item-title">
                        <img
                          width="26"
                          src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                          alt="菱形图标"
                        />
                        <span class="study-item-title-text">要点提醒</span>
                      </div>
                      <p class="study-item-content">
                        {{ module.studyReminder }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 综合建议模块 -->
            <div
              class="comprehensive-advice-section"
              v-if="studyPlanData.comprehensiveAdvice"
            >
              <!-- 建议标题 -->
              <div class="step-header">
                <div class="step-num-tag">
                  <img
                    src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitle-bg.png"
                    alt="背景"
                  />
                  <span class="step-number">04</span>
                  <span class="step-text">综合建议</span>
                </div>
              </div>
              <!-- 建议内容 -->
              <div class="advice-content">
                <div class="advice-content-container">
                  <p class="advice-text">
                    {{ studyPlanData.comprehensiveAdvice }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- PDF生成按钮 -->
    <div class="pdf-generate-container">
      <button
        class="generate-pdf-btn"
        @click="handleGeneratePdf"
        :disabled="isGeneratingPdf"
      >
        <span v-if="!isGeneratingPdf">生成PDF</span>
        <span v-else>生成中...</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, nextTick, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import type { ECharts, EChartsOption } from "echarts";
import {
  getStudyPlanFromDatabase,
  getTargetScores,
  getReportBasicInfo,
  getReportDetailNew,
} from "@/api/report";
import { getNationalLineData } from "@/api/school";
import type {
  SchoolListItem,
  RecommendSchool,
  HighRecommendSchool,
} from "@/types";
import { PreserveStylePdfGenerator } from "@/utils/preserveStylePdfGenerator";
import { ElMessage } from "element-plus";

// 学习计划相关接口
interface WeakModuleAnalysis {
  id?: number;
  subject: string;
  problemAnalysis: string;
  solutions: string;
}

interface StudyModule {
  id: string;
  dbId?: number;
  name: string;
  studyContent: string;
  studyMethod: string;
  studyMaterials: string;
  studyReminder: string;
}

interface StudyStage {
  id: string;
  title: string;
  modules: StudyModule[];
}

interface StudyPlanning {
  title: string;
  stages: StudyStage[];
}

interface StudyPlanData {
  weakModuleAnalysis: WeakModuleAnalysis[];
  studyPlanning: StudyPlanning;
  comprehensiveAdvice: string;
  comprehensiveAdviceId?: number;
}

interface TargetScoreData {
  politics: number;
  english: number;
  business1: number;
  business2: number;
  total: number;
}

interface NationalLineData {
  subject_code: string;
  subject_name: string;
  years: string[];
  a_total: number[];
  a_single_100: number[];
  a_single_over100: number[];
  b_total: number[];
  b_single_100: number[];
  b_single_over100: number[];
}

// 学习计划相关接口
interface WeakModuleAnalysis {
  id?: number;
  subject: string;
  problemAnalysis: string;
  solutions: string;
}

interface StudyModule {
  id: string;
  dbId?: number;
  name: string;
  studyContent: string;
  studyMethod: string;
  studyMaterials: string;
  studyReminder: string;
}

interface StudyStage {
  id: string;
  title: string;
  modules: StudyModule[];
}

interface StudyPlanning {
  title: string;
  stages: StudyStage[];
}

interface StudyPlanData {
  weakModuleAnalysis: WeakModuleAnalysis[];
  studyPlanning: StudyPlanning;
  comprehensiveAdvice: string;
  comprehensiveAdviceId?: number;
}

interface TargetScoreData {
  politics: number;
  english: number;
  business1: number;
  business2: number;
  total: number;
}

interface NationalLineData {
  subject_code: string;
  subject_name: string;
  years: string[];
  a_total: number[];
  a_single_100: number[];
  a_single_over100: number[];
  b_total: number[];
  b_single_100: number[];
  b_single_over100: number[];
  first_level_discipline?: string;
}

// 实际数据
const schoolListData = ref<SchoolListItem[]>([]);
const highRecommendData = ref<HighRecommendSchool[]>([]);
//根据学校名称 获取分数差
const getDiffScore = (school_info_id: number) => {
  const school = schoolListData.value.find((item) => item.id == school_info_id);
  return school?.score_diff;
};
interface undergraduateTranscriptItem {
  id: number;
  title: string;
  score: string;
}
// 报告表单数据
const reportForm = reactive({
  name: "", // 学员姓名
  sex: "", // 性别
  undergraduateSchool: "", // 本科院校
  undergraduateMajor: "", // 本科专业
  targetMajor: "", // 目标专业
  majorCode: "", // 专业代码
  phone: "", // 联系方式
  examYear: "", // 考研年份
  isMultiDisciplinary: "", // 跨专业
  educationalStyle: "", // 培养方式
  englishScore: "", // 高考英语成绩
  mathScore: "", // 高考数学成绩
  undergraduateTranscript: [] as undergraduateTranscriptItem[],
  cet4: "", // 大学四级
  cet6: "", // 大学六级
  tofelScore: "", // 托福
  ieltsScore: "", // 雅思
  englishLevel: "", // 英语能力
  region: [] as string[], // 地区倾向
  intendedSchools: [] as string[], // 省份选择
  targetSchool: "", // 梦校
  targetSchoolName: "", // 梦校名称
  schoolLevel: "", // 院校层次
  referenceBooks: "", // 专业课指定参考书
  politics: "", // 政治成绩
  englishS: "", // 英语成绩
  englishType: "", // 业务课一成绩
  mathType: "", // 业务课二成绩
  professional: "", // 专业课成绩
  totalScore: "", // 总分
  personalNeeds: "", // 个性化需求
  weakModules: "", // 薄弱模块
});

// 学习计划数据
const studyPlanData = ref<StudyPlanData | null>(null);

// 目标分数数据
const targetScoreData = ref<TargetScoreData | null>(null);

// 国家线数据
const nationalLineData = ref<NationalLineData | null>(null);

// 当前报告ID
const currentReportId = ref<number | string>("");

// 真实的院校推荐数据
const realRecommendData = ref<RecommendSchool[]>([]);

// 图表引用
const chartA = ref<HTMLDivElement>();
const chartB = ref<HTMLDivElement>();
let chartInstanceA: ECharts | null = null;
let chartInstanceB: ECharts | null = null;

// PDF生成相关
const isGeneratingPdf = ref(false);

// Loading 相关状态
const isLoading = ref(false);
const loadingProgress = ref(0);
const loadingTasks = ref<string[]>([]);
const completedTasks = ref<string[]>([]);

const truncateText = (str: string, maxLength: number = 6): string => {
  return str.length > maxLength ? str.slice(0, maxLength) + "..." : str;
};

let firstLevelDiscipline = "";
// Loading 进度管理函数
const updateLoadingProgress = () => {
  if (loadingTasks.value.length === 0) {
    loadingProgress.value = 0;
    return;
  }

  const progress =
    (completedTasks.value.length / loadingTasks.value.length) * 100;
  loadingProgress.value = Math.round(progress);

  // 如果所有任务完成，延迟一下再隐藏loading
  if (completedTasks.value.length === loadingTasks.value.length) {
    setTimeout(() => {
      isLoading.value = false;
    }, 500);
  }
};

const startLoading = (tasks: string[]) => {
  isLoading.value = true;
  loadingTasks.value = [...tasks];
  completedTasks.value = [];
  loadingProgress.value = 0;
};

const completeTask = (taskName: string) => {
  if (!completedTasks.value.includes(taskName)) {
    completedTasks.value.push(taskName);
    updateLoadingProgress();
  }
};

//处理一级专业括号和数字
const filterMajor = (str: string | undefined) => {
  if (str) {
    return str.replace(/\s*\(\d+\)/, "");
  } else {
    return "";
  }
};
// 生成图表配置 - 使用真实国家线数据
const chartOption = (isARegion: boolean = true): EChartsOption => {
  console.log(`生成${isARegion ? "A区" : "B区"}图表配置`);

  // 如果没有传入国家线数据，使用默认数据
  const nationalData = nationalLineData.value || {
    subject_code: "",
    subject_name: "",
    years: [],
    a_total: [],
    a_single_100: [],
    a_single_over100: [],
    b_total: [],
    b_single_100: [],
    b_single_over100: [],
  };

  console.log("国家线数据:", nationalData);

  // 根据区域选择对应的数据
  const years: string[] =
    nationalData.years.length > 0
      ? nationalData.years
      : ["2021", "2022", "2023", "2024", "2025"];

  const totalScores: number[] = isARegion
    ? nationalData.a_total.length > 0
      ? nationalData.a_total
      : [360, 370, 360, 370, 370]
    : nationalData.b_total.length > 0
    ? nationalData.b_total
    : [340, 350, 340, 350, 350];

  const single100Scores: number[] = isARegion
    ? nationalData.a_single_100.length > 0
      ? nationalData.a_single_100
      : [60, 65, 60, 65, 65]
    : nationalData.b_single_100.length > 0
    ? nationalData.b_single_100
    : [55, 60, 55, 60, 60];

  const singleOver100Scores: number[] = isARegion
    ? nationalData.a_single_over100.length > 0
      ? nationalData.a_single_over100
      : [90, 95, 90, 95, 95]
    : nationalData.b_single_over100.length > 0
    ? nationalData.b_single_over100
    : [85, 90, 85, 90, 90];

  console.log(`${isARegion ? "A区" : "B区"}图表数据:`, {
    years,
    totalScores,
    single100Scores,
    singleOver100Scores,
  });

  const option: EChartsOption = {
    title: {
      left: "left",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#1bb394",
      },
      top: 5,
    },
    grid: {
      left: 20,
      right: 20,
      top: 50,
      bottom: 10,
      containLabel: true,
    },
    legend: {
      data: ["总分", "单科(满分=100)", "单科(满分>100)"],
      right: 10,
      top: 15,
      icon: "rect",
      itemWidth: 16,
      itemHeight: 8,
      textStyle: {
        fontSize: 12,
      },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0,0,0,0.7)",
      borderRadius: 8,
      textStyle: { color: "#fff" },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: years,
      axisLine: {
        show: true,
        lineStyle: { color: "#1bb394" },
      },
      axisLabel: {
        color: "#666",
        fontSize: 12,
      },
    },
    yAxis: {
      type: "value",
      offset: 5,
      min: 0,
      max: 400,
      interval: 50,
      splitLine: {
        show: true,
        lineStyle: {
          color: "#eee",
          type: "dashed",
        },
      },
      axisLine: { show: false },
      axisLabel: {
        color: "#666",
        fontSize: 12,
      },
    },
    series: [
      {
        name: "总分",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(255, 153, 0, 0.3)", // 顶部颜色
              },
              {
                offset: 1,
                color: "rgba(255, 153, 0, 0.1)", // 底部颜色
              },
            ],
            global: false,
          },
        },
        lineStyle: {
          color: "#ff9900",
          width: 2,
        },
        itemStyle: {
          color: "#ff9900",
          borderWidth: 2,
          borderColor: "#fff",
        },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#ff9900",
          fontWeight: "bold",
          offset: [0, -8],
        },
        data: totalScores,
      },
      {
        name: "单科(满分=100)",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(27, 179, 148, 0.3)", // 顶部颜色
              },
              {
                offset: 1,
                color: "rgba(27, 179, 148, 0.1)", // 底部颜色
              },
            ],
            global: false,
          },
        },
        lineStyle: {
          color: "#1bb394",
          width: 2,
        },
        itemStyle: {
          color: "#1bb394",
          borderWidth: 2,
          borderColor: "#fff",
        },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#1bb394",
          fontWeight: "bold",
          offset: [0, -8],
        },
        data: single100Scores,
      },
      {
        name: "单科(满分>100)",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(255, 99, 132, 0.3)", // 顶部颜色
              },
              {
                offset: 1,
                color: "rgba(255, 99, 132, 0.1)", // 底部颜色
              },
            ],
            global: false,
          },
        },
        lineStyle: {
          color: "#ff6384",
          width: 2,
        },
        itemStyle: {
          color: "#ff6384",
          borderWidth: 2,
          borderColor: "#fff",
        },
        label: {
          show: true,
          position: "top",
          fontSize: 12,
          color: "#ff6384",
          fontWeight: "bold",
          offset: [0, -20],
        },
        data: singleOver100Scores,
      },
    ],
  };

  console.log("生成的图表配置:", option);
  return option;
};

// 初始化图表
const initCharts = (): void => {
  console.log("initCharts 被调用");

  setTimeout(() => {
    console.log("开始初始化图表...");
    console.log("chartA.value:", chartA.value);
    console.log("chartB.value:", chartB.value);

    if (chartA.value) {
      const rect = chartA.value.getBoundingClientRect();
      console.log("A区图表容器尺寸:", rect);

      if (rect.width > 0 && rect.height > 0) {
        try {
          if (chartInstanceA) {
            console.log("销毁已存在的A区图表");
            chartInstanceA.dispose();
          }
          console.log("开始初始化A区图表");
          chartInstanceA = echarts.init(chartA.value);
          const optionA = chartOption(true);
          console.log("A区图表配置:", optionA);
          chartInstanceA.setOption(optionA);
          console.log("A区图表初始化成功");
        } catch (error) {
          console.error("A区图表初始化失败:", error);
        }
      } else {
        console.warn("A区图表容器尺寸为0，延迟重试");
        setTimeout(() => initCharts(), 500);
        return;
      }
    } else {
      console.warn("A区图表容器不存在");
    }

    if (chartB.value) {
      const rect = chartB.value.getBoundingClientRect();
      console.log("B区图表容器尺寸:", rect);

      if (rect.width > 0 && rect.height > 0) {
        try {
          if (chartInstanceB) {
            console.log("销毁已存在的B区图表");
            chartInstanceB.dispose();
          }
          console.log("开始初始化B区图表");
          chartInstanceB = echarts.init(chartB.value);
          const optionB = chartOption(false);
          console.log("B区图表配置:", optionB);
          chartInstanceB.setOption(optionB);
          console.log("B区图表初始化成功");
        } catch (error) {
          console.error("B区图表初始化失败:", error);
        }
      } else {
        console.warn("B区图表容器尺寸为0，延迟重试");
        setTimeout(() => initCharts(), 500);
        return;
      }
    } else {
      console.warn("B区图表容器不存在");
    }
  }, 500); // 增加延迟时间
};

// 处理窗口大小变化
const handleResize = (): void => {
  setTimeout(() => {
    if (chartInstanceA) {
      chartInstanceA.resize();
    }
    if (chartInstanceB) {
      chartInstanceB.resize();
    }

    // 如果图表未初始化，重新初始化
    if (!chartInstanceA || !chartInstanceB) {
      initCharts();
    }
  }, 100);
};

// 加载学习计划数据
const loadStudyPlanData = async (reportId: number | string) => {
  try {
    console.log("开始加载学习计划数据，报告ID:", reportId);
    const response = await getStudyPlanFromDatabase({ report_id: reportId });
    console.log("学习计划API响应:", response);

    if (response.code === 0 && response.data) {
      studyPlanData.value = response.data;
      console.log("学习计划数据加载成功:", studyPlanData.value);
    } else {
      console.warn("学习计划数据加载失败:", response.msg);
    }
  } catch (error) {
    console.error("加载学习计划数据失败:", error);
  } finally {
    completeTask("学习计划数据");
  }
};

// 加载目标分数数据
const loadTargetScoreData = async (reportId: number | string) => {
  try {
    console.log("开始加载目标分数数据，报告ID:", reportId);
    const response = await getTargetScores(reportId);
    console.log("目标分数API响应:", response);

    if (response.code === 0 && response.data) {
      targetScoreData.value = response.data;
      console.log("目标分数数据加载成功:", targetScoreData.value);
    } else {
      console.warn("目标分数数据加载失败:", response.msg);
    }
  } catch (error) {
    console.error("加载目标分数数据失败:", error);
  } finally {
    completeTask("目标分数数据");
  }
};

// 加载国家线数据
const loadNationalLineData = async (majorCode: string) => {
  try {
    console.log("开始加载国家线数据，专业代码:", majorCode);
    const response = await getNationalLineData(majorCode);
    console.log("国家线数据API响应:", response);

    if (response.code === 0 && response.data) {
      nationalLineData.value = response.data;
      console.log("国家线数据加载成功:", nationalLineData.value);

      // 重新初始化图表
      setTimeout(() => {
        initCharts();
      }, 100);
    } else {
      console.warn("国家线数据加载失败:", response.msg);
    }
  } catch (error) {
    console.error("加载国家线数据失败:", error);
  } finally {
    completeTask("国家线数据");
  }
};

// 加载院校推荐数据
const loadRecommendationData = async (reportId: number | string) => {
  try {
    console.log("开始加载院校推荐数据，报告ID:", reportId);
    const response = await getReportDetailNew({ report_id: reportId });
    console.log("院校推荐数据API响应:", response);

    if (response.code === 0 && response.data) {
      const { recommend_list, high_recommend_list, school_list } =
        response.data;

      // 更新真实推荐数据
      if (recommend_list && Array.isArray(recommend_list)) {
        console.log("原始推荐数据:", recommend_list);
        realRecommendData.value = recommend_list.map((school: any) => {
          console.log("处理学校数据:", school.school_name, school.basic_info);
          return {
            school_id: school.school_id,
            school_name: school.school_name,
            major_name: school.major_name,
            difficulty_analysis: school.difficulty_analysis,
            suggest: school.suggest,
            reason: school.reason,
            basic_info: school.basic_info || {},
            school_info: school.school_info || {},
            admission_stats: school.admission_stats || { has_data: false },
            retest_stats: school.retest_stats || { has_data: false },
            current_year_retest_list: school.current_year_retest_list || {
              list: [],
              year: null,
              count: 0,
            },
            current_year_admission_list: school.current_year_admission_list || {
              list: [],
              year: null,
              count: 0,
            },
            admission_data: school.admission_data || [],
          };
        });
        console.log(
          "院校推荐数据加载成功，共",
          recommend_list.length,
          "所院校"
        );
      }

      // 更新院校总览数据
      if (school_list && Array.isArray(school_list)) {
        schoolListData.value = school_list;
        console.log("院校总览数据加载成功，共", school_list.length, "所院校");
      }

      // 更新高性价比推荐数据
      if (high_recommend_list) {
        highRecommendData.value = [high_recommend_list];
        console.log(
          "高性价比推荐数据加载成功:",
          high_recommend_list.school_name
        );
      }
    } else {
      console.warn("院校推荐数据加载失败:", response.msg);
    }
  } catch (error) {
    console.error("加载院校推荐数据失败:", error);
  } finally {
    completeTask("院校推荐数据");
  }
};

// 加载学员基本信息数据
const loadReportBasicInfo = async (reportId: number | string) => {
  try {
    console.log("开始加载学员基本信息，报告ID:", reportId);
    const response = await getReportBasicInfo(reportId);
    console.log("学员基本信息API响应:", response);

    if (response.code === 0 && response.data) {
      const { student_info, report_info } = response.data;

      // 更新reportForm数据
      if (student_info) {
        reportForm.name = student_info.name || "";
        reportForm.sex = student_info.sexText || "";
        reportForm.undergraduateSchool =
          student_info.undergraduateSchoolName || "";
        reportForm.undergraduateMajor =
          student_info.undergraduateMajorName || "";
        reportForm.targetMajor = report_info.target_major || "";
        reportForm.majorCode = student_info.majorCode || "";
        reportForm.phone = student_info.phone || "";
        reportForm.examYear = student_info.examYear || "";
        reportForm.isMultiDisciplinary = student_info.isCrossMajorText || "";
        reportForm.educationalStyle = student_info.educationalStyleText || "";
        reportForm.englishScore = student_info.englishScore || "";
        reportForm.cet4 = student_info.cet4 || "";
        reportForm.cet6 = student_info.cet6 || "";
        reportForm.tofelScore = student_info.tofelScore || "";
        reportForm.ieltsScore = student_info.ieltsScore || "";
        reportForm.undergraduateTranscript =
          student_info.undergraduateTranscript || [];
        reportForm.englishLevel = student_info.englishAbility || "";
      }

      // 更新报告相关信息
      if (report_info) {
        reportForm.politics = report_info.politics_score || "";
        reportForm.englishS = report_info.english_score || "";
        reportForm.englishType = report_info.english_type || "";
        reportForm.mathType = report_info.math_type || "";
        reportForm.professional = report_info.professional_score || "";
        reportForm.totalScore = report_info.total_score || "";
        reportForm.personalNeeds = report_info.personal_needs || "";
        reportForm.weakModules = report_info.weak_modules || "";

        // 院校信息
        reportForm.region = report_info.region_preference || "";
        reportForm.intendedSchools = report_info.province_selection || "";
        reportForm.targetSchoolName = report_info.dream_school || "";
        reportForm.schoolLevel = report_info.school_level || "";
        reportForm.referenceBooks = report_info.reference_books || "";
      }

      console.log("学员基本信息加载成功，已更新reportForm");
      await loadNationalLineData(firstLevelDiscipline);
    } else {
      console.warn("学员基本信息加载失败:", response.msg);
    }
  } catch (error) {
    console.error("加载学员基本信息失败:", error);
  } finally {
    completeTask("学员基本信息");
  }
};

// 初始化PDF数据（暴露给父组件调用）
const initPdfData = async (
  reportId: number | string,
  firstLevelcode: string
) => {
  currentReportId.value = reportId;
  firstLevelDiscipline = firstLevelcode;
  // 启动loading状态
  const tasks = [
    "学员基本信息",
    "学习计划数据",
    "目标分数数据",
    "院校推荐数据",
    "国家线数据",
  ];
  startLoading(tasks);

  try {
    await Promise.all([
      loadStudyPlanData(reportId),
      loadTargetScoreData(reportId),
      loadReportBasicInfo(reportId),
      loadRecommendationData(reportId),
    ]);
  } catch (error) {
    console.error("加载PDF数据失败:", error);
    // 即使出错也要隐藏loading
    isLoading.value = false;
  }
};

// 处理PDF生成
const handleGeneratePdf = async () => {
  if (isGeneratingPdf.value) return;

  try {
    isGeneratingPdf.value = true;
    ElMessage.info("正在生成PDF，请稍候...");

    // 获取报告容器元素
    const reportContainer = document.querySelector(
      ".generate-pdf-container .report-container"
    ) as HTMLElement;
    if (!reportContainer) {
      throw new Error("未找到报告容器元素");
    }

    // 生成PDF文件名
    const filename = `AI择校报告_${reportForm.name || "未命名"}_${new Date()
      .toISOString()
      .slice(0, 10)}.pdf`;

    // 使用保持样式的PDF生成器
    await PreserveStylePdfGenerator.generatePdf(reportContainer, {
      filename,
      quality: 0.95,
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      reportId: currentReportId.value, // 传递报告ID
      autoUpload: true, // 启用自动上传
    });

    ElMessage.success("PDF生成成功！");
  } catch (error) {
    console.error("PDF生成失败:", error);
    ElMessage.error("PDF生成失败，请重试");
  } finally {
    isGeneratingPdf.value = false;
  }
};

// 组件挂载后初始化图表
onMounted(() => {
  console.log("组件已挂载，开始初始化图表");
  nextTick(() => {
    initCharts();
    // 添加窗口resize监听
    window.addEventListener("resize", handleResize);

    // 额外的延迟初始化，确保DOM完全渲染
    setTimeout(() => {
      if (!chartInstanceA || !chartInstanceB) {
        console.log("图表未正确初始化，重新尝试");
        initCharts();
      }
    }, 1000);
  });
});

// 清理资源
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);

  // 销毁图表实例
  if (chartInstanceA) {
    chartInstanceA.dispose();
    chartInstanceA = null;
  }
  if (chartInstanceB) {
    chartInstanceB.dispose();
    chartInstanceB = null;
  }
});

// 暴露方法给父组件
defineExpose({
  initPdfData,
  handleGeneratePdf,
});
</script>

<style scoped lang="less">
.generate-pdf-container {
  background-color: #f5f5f5;
  height: 70vh;
  overflow: scroll;
  position: relative;
}

/* Loading 遮罩层样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.loading-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(27, 179, 148, 0.2);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1bb394;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 16px;
  color: #333;
  margin-bottom: 20px;
  font-weight: 500;
}

.loading-progress {
  width: 300px;
  margin: 0 auto;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1bb394, #16a085);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* PDF生成按钮容器 */
.pdf-generate-container {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

/* PDF生成按钮样式 */
.generate-pdf-btn {
  background: linear-gradient(135deg, #1bb394, #16a085);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(27, 179, 148, 0.3);
  transition: all 0.3s ease;
  min-width: 120px;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #16a085, #1bb394);
    box-shadow: 0 6px 20px rgba(27, 179, 148, 0.4);
    transform: translateY(-2px);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(27, 179, 148, 0.3);
  }

  &:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
  }
}

.report-container {
  width: 800px;
  background: white;
  margin: 0 auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 报告封面头部样式 */
.report-header {
  height: 1122px;
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/report_pdf_front.png");
  background-repeat: no-repeat;
  background-size: contain;
  position: relative;

  .stu-name {
    position: absolute;
    top: 36%;
    left: 6%;
    font-size: 30px;
    font-weight: bold;
  }
}

/* 报告内容页面样式 */
.report-content {
  background: white;
  position: relative;
  /*padding-bottom: 100px;*/
  /* 为footer预留空间 */
}

.step-title-bg {
  width: 100%;
  height: 38px;
  height: auto;
  margin-bottom: 20px;
}

.first-step {
  padding: 0 40px;
  /* margin-top: 30px;*/
}

/* 图表相关样式 - 基于MajorAnalysis.vue */
.chart-section {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  align-items: center;

  .region-label {
    color: #5a5a5a;
    padding: 8px 0;
    border-radius: 4px;
    font-weight: bold;
    margin-right: 20px;
    min-width: 50px;
    text-align: left;
  }

  .chart-info {
    display: flex;
    gap: 20px;

    span {
      font-size: 14px;
      color: #5a5a5a;
    }
  }
}

.chart-container {
  padding: 10px 0;
  background: white;
}

/* 图表样式 - 基于MajorAnalysis.vue */
.echarts-box {
  width: 100%;
  height: 280px;
  border: 1px solid #1bb394;
  border-radius: 12px;
  background: #fff;
  box-sizing: border-box;
  padding: 5px;
}

/* 院校信息表格样式 */
.school-info-section {
  margin-bottom: 40px;
}

.school-info-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  th {
    border: none;
    background: #d8ffed;
    color: #5a5a5a;
    font-weight: 600;
    text-align: center;
    font-size: 12px;
    padding: 27px 0px;
    border: none;
    white-space: nowrap;
    width: 140px;

    &:first-child {
      width: 50px;
      padding-left: 20px;
    }

    &:last-child {
      padding-right: 20px;
    }
  }

  td {
    border: none;
    text-align: center;
    border: none;
    font-size: 12px;
    color: #333;
    vertical-align: middle;
    width: 140px;

    &:first-child {
      padding-left: 20px;
      width: 50px;
      font-weight: 600;
    }

    &:nth-child(2) {
      text-align: center;
      /* 改为居中对齐 */
      padding: 0;
      /* 移除左边距，让内容完全居中 */
      height: 60px;
      /* 给容器设置固定高度 */

      > div {
        padding-top: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        /* 水平居中 */
        justify-content: center;
        /* 垂直居中 */
        gap: 6px;
        height: 100%;
        /* 占满父容器高度 */

        > span {
          font-weight: 500;
          color: #333;
        }
      }
    }
  }

  tbody tr:nth-child(odd) {
    background: white;
  }

  tbody tr:nth-child(even) {
    background: #f8f9fa;
  }
}

.school-tags {
  display: flex;
  gap: 4px;
  justify-content: center;
  /* 标签也居中对齐 */
  padding-bottom: 8px;

  .tag {
    display: inline-block;
    border-radius: 4px;
    font-size: 8px;
    font-weight: 500;
    color: white;
    width: 28px;
    height: 18px;
    line-height: 18px;
    text-align: center;

    &.tag-double {
      background: #ff9b3a;
    }

    &.tag-985 {
      background: #caa0ff;
    }

    &.tag-211 {
      background: #92baff;
    }
  }
}

/* 步骤内容样式 */
.step-section {
  margin-bottom: 26px;
}

.step-header {
  position: relative;

  .step-num-tag {
    position: relative;
    display: inline-block;

    img {
      height: 35px;
    }

    .step-number {
      width: 30px;
      height: 30px;
      position: absolute;
      left: 6px;
      line-height: 30px;
      text-align: center;
      top: 50%;
      transform: translateY(-50%);
      color: white;
      font-weight: bold;
      font-size: 16px;
    }

    .step-text {
      position: absolute;
      left: -56px;
      width: 100%;
      text-align: left;
      top: 46%;
      transform: translateY(-50%);
      color: white;
      font-weight: bold;
      font-size: 14px;
      color: #1bb394;
      padding-left: 108px;
    }
  }
}

.step-content {
  border-radius: 8px;
}
.form-undergraduate {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  .form-item {
    width: 31%;
  }
}
.form-grid {
  .form-row {
    display: flex;
    margin-bottom: 15px;
    gap: 20px;

    &.full-width {
      .form-item {
        flex: 1;
      }
    }

    .form-item {
      flex: 1;
      display: flex;
      align-items: center;

      .label {
        color: #5a5a5a;
        font-weight: bold;
        margin-right: 8px;
        white-space: nowrap;
      }

      .value {
        color: #666;
        flex: 1;
      }

      &.full-width {
        flex: 1 1 100%;
      }
    }
  }
}

/* 成绩表格样式 */
.score-table {
  margin-bottom: 20px;
  border: 1px solid #1bb394;
  overflow: hidden;
  border-radius: 8px;

  table {
    width: 100%;
    border-collapse: collapse;
    background: white;

    th,
    td {
      padding: 12px 15px;
      text-align: center;
      border-left: 1px solid #1bb394;

      &:first-child {
        border-left: none;
      }
    }

    th {
      background: #f5fffd;
      color: #333;
      font-weight: 600;
      border-bottom: 1px solid #1bb394;
    }

    td {
      color: #333;
    }
  }
}

.weak-modules {
  border: 1px solid #1bb394;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    color: #5a5a5a;
    font-weight: 600;
    margin-right: 8px;
  }

  .value {
    color: #5a5a5a;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-header {
    height: auto;
    min-height: 400px;

    .header-content {
      flex-direction: column;
      text-align: center;
      padding: 30px 20px;

      .logo-section {
        margin-bottom: 20px;

        .header-logo {
          height: 60px;
        }
      }

      .title-section {
        padding: 0;
        margin-bottom: 20px;

        .report-title {
          font-size: 28px;
        }

        .report-subtitle {
          font-size: 14px;
        }
      }

      .decoration-section {
        .decoration-image {
          height: 80px;
        }
      }
    }
  }

  .steps {
    padding: 20px;
  }
}

/* 新增样式 */
.step-num-tag {
  background-image: url("https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/subtitlebg.png");
  width: 265px;
  height: 40px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  margin-bottom: 14px;
}

// .step-num-tag span {
//   width: 30px;
//   height: 30px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   color: white;
//   border-radius: 50%;
//   font-weight: bold;
//   padding-left: 13px;
// }

.tag-text {
  color: #1bb394;
  font-weight: bold;
}

/* 院校详情卡片样式 */
.school-detail-card {
  background-color: #fff;
  overflow: hidden;
}

.school-header {
  display: flex;
}

.school-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 90%;
    height: 90%;
    object-fit: contain;
  }
}

.school-info {
  flex: 1;
}

.school-title {
  display: flex;
  align-items: baseline;

  h2 {
    font-size: 22px;
    color: #333;
    margin: 0;
    margin-right: 12px;
  }

  .school-location {
    font-size: 18px;
    color: #5a5a5a;
  }
}

.school-tags-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  .tag {
    display: inline-block;
    width: 52px;
    height: 25px;
    border-radius: 4px;
    color: #fff;
    text-align: center;
    line-height: 25px;
    font-size: 14px;
    margin-right: 5px;

    &.tag-double {
      background-color: #ff9b3a;
    }

    &.tag-985 {
      background-color: #caa0ff;
    }

    &.tag-211 {
      background-color: #92baff;
    }
  }

  .major-name,
  .score-diff {
    padding: 10px;
    font-weight: normal;
    font-size: 18px;
    color: #5a5a5a;
    line-height: 36px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .score-diff {
    margin-left: -16px;
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  position: relative;
  margin-bottom: 15px;
}

.school-detail-section {
  margin-bottom: 20px;
  padding: 10px 20px 20px;
  border-radius: 12px 12px 12px 12px;
  border: 1px dashed #2fc293;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
}

.detail-item {
  display: flex;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.item-content {
  flex: 1;

  h4 {
    font-size: 16px;
    color: #333;
    margin: 0 0 8px 0;
    font-weight: 600;
  }

  p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin: 0 0 5px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 招生情况样式 */
.step-content-zs {
  width: 100%;
  margin-top: 20px;
}

.admission-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 20px 0 15px 0;
  position: relative;
  padding-left: 12px;
}

.admission-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
}

.admission-table th {
  background-color: #dcf7f0;
  color: #333;
  font-weight: bold;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
}

.admission-table td {
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
  background-color: #fff;
}

.admission-table,
.admission-table th,
.admission-table td {
  border: none !important;
}

.admission-table tr:nth-child(even) td {
  background-color: #f9f9f9;
}

.reexam-container {
  border: 1px dashed #2fc293;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
}

.reexam-card {
  padding: 0 20px 20px;
}

.reexam-card:last-child {
  border-bottom: none;
}

.reexam-header {
  display: flex;
  align-items: center;
  padding: 15px 0;

  img {
    width: 27px;
    height: 21px;
    margin-right: 8px;
  }
}

.reexam-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.reexam-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.reexam-content p {
  margin: 8px 0;
}

.recommend-school-wraper {
  /* 推荐院校包装器样式 */
}

/* 推荐综合性价比高的院校样式 */
.recommend-school-container {
  margin-top: 28px;
  border: 1px dashed #2fc293;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
  background-color: #fff;
}

.recommend-school-card {
  padding: 0 20px 20px;
}

.recommend-school-header {
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.recommend-icon {
  margin-right: 8px;

  img {
    width: 27px;
    height: 21px;
  }
}

.recommend-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.recommend-school-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.recommend-school-content p {
  margin: 8px 0;
}
.recommend-school-wraper {
  .step-num-tag {
    position: relative;
    .step-number {
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: white;
      font-weight: bold;
      font-size: 16px;
      border-radius: 50%;
    }
    .tag-text {
      padding-left: 57px;
    }
  }
}

/* 学习计划相关样式 */
.weak-module-container {
  border: 1px dashed #1bb394;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
  border-radius: 10px;
  margin-bottom: 30px;
  padding: 20px 0;
}

.weak-module-item {
  padding: 0 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}

.detail-section,
.analysis-section,
.solutions-section {
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

.subtitle {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  img {
    margin-right: 8px;
  }

  .subtitle-text {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }
}

.detail-text,
.analysis-text,
.solution-text {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
  margin: 0;
  padding-left: 34px;
}

/* 目标分数表格样式 */
.target-score-section {
  margin-bottom: 20px;
  padding: 0 20px;
}

.target-score-header {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 15px;

  .target-score-title {
    height: 20px;
    line-height: 20px;
    font-weight: bold;
    font-size: 16px;
    color: #5a5a5a;
  }
}

.target-score-table {
  border: 1px solid #1bb394;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.score-row {
  display: flex;
}

.header-row {
  background: #f5fffd;
}

.score-cell {
  flex: 1;
  padding: 12px 15px;
  text-align: center;
  border-right: 1px solid #1bb394;
  font-weight: 600;
  color: #333;

  &:last-child {
    border-right: none;
  }
}

.bottom-cell {
  border-top: 1px solid #1bb394;
  background: white;
  font-weight: normal;
}

/* 学习规划样式 */
.study-planning-section {
  margin-bottom: 30px;
}

.stage-section {
  margin-bottom: 25px;

  &:last-child {
    margin-bottom: 0;
  }
}

.stage-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}
.stage-content {
  padding: 20px 0;
  border: 1px dashed #2fc293;
  border-radius: 10px;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
}
.module-container {
  margin-bottom: 20px;
  padding: 0 20px;

  border-radius: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.module-title {
  margin-bottom: 15px;
  padding-bottom: 8px;

  .module-title-text {
    font-size: 16px;
    font-weight: bold;
  }
}

.study-item {
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

.study-item-title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  img {
    margin-right: 8px;
  }

  .study-item-title-text {
    font-size: 16px;
    color: #4c5370;
    font-weight: bold;
  }
}

.study-item-content {
  font-size: 14px;
  color: #504e4e;
  line-height: 24px;
  margin: 0;
  white-space: pre-wrap; /* 保持换行符和空格 */
}

/* 综合建议样式 */
.comprehensive-advice-section {
  margin-bottom: 30px;
  white-space: pre-wrap;
}

.advice-content {
  margin-top: 15px;
}

.advice-content-container {
  padding: 20px;
  border: 1px dashed #2fc293;
  background: linear-gradient(180deg, #e6f5ef 0%, rgba(255, 255, 255, 0) 40%);
  border-radius: 8px;
}

.advice-text {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
  margin: 0;
}
</style>
